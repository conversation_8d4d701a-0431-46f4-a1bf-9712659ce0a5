import pandas as pd
import numpy as np
from sklearn.linear_model import LinearRegression
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.model_selection import train_test_split
import warnings
warnings.filterwarnings('ignore')

class ModelTrainer:
    def __init__(self):
        self.models = {
            'Linear Regression': LinearRegression(),
            'Random Forest': RandomForestRegressor(n_estimators=100, random_state=42)
        }
        self.scalers = {}
        self.best_models = {}
        self.feature_columns = ['Miscellaneous', 'Financial', 'CapEx', 'COGS', 'Operating']
    
    def train_and_forecast(self, df, forecast_months):
        """
        Train models and generate forecasts
        Returns: dict with forecast dataframe and performance metrics
        """
        # Prepare features
        X, y_dict = self._prepare_features(df)
        
        # Train models for each expense category and total
        model_performance = {}
        forecasts = {}
        
        # Train models for each category
        for category in self.feature_columns + ['Total']:
            if category in df.columns:
                y = df[category].values
                
                # Split data for validation
                X_train, X_test, y_train, y_test = train_test_split(
                    X, y, test_size=0.2, random_state=42, shuffle=False
                )
                
                # Train and evaluate models
                best_model, best_score, category_performance = self._train_category_models(
                    X_train, X_test, y_train, y_test, category
                )
                
                self.best_models[category] = best_model
                model_performance[category] = category_performance
                
                # Generate forecast for this category
                forecast_values = self._generate_category_forecast(
                    best_model, df, category, forecast_months
                )
                forecasts[category] = forecast_values
        
        # Create forecast dataframe
        forecast_df = self._create_forecast_dataframe(df, forecasts, forecast_months)
        
        return {
            'forecast_df': forecast_df,
            'performance': model_performance
        }
    
    def _prepare_features(self, df):
        """Prepare features for model training"""
        # Create time-based features
        df['Month_dt'] = pd.to_datetime(df['Month'])
        df['Month_num'] = (df['Month_dt'] - df['Month_dt'].min()).dt.days / 30.44  # Convert to months
        df['Year'] = df['Month_dt'].dt.year
        df['Month_of_year'] = df['Month_dt'].dt.month
        
        # Create lag features (previous month values)
        lag_features = []
        for col in self.feature_columns:
            if col in df.columns:
                df[f'{col}_lag1'] = df[col].shift(1)
                df[f'{col}_lag2'] = df[col].shift(2)
                df[f'{col}_lag3'] = df[col].shift(3)
                lag_features.extend([f'{col}_lag1', f'{col}_lag2', f'{col}_lag3'])
        
        # Create moving averages
        ma_features = []
        for col in self.feature_columns:
            if col in df.columns:
                df[f'{col}_ma3'] = df[col].rolling(window=3).mean()
                df[f'{col}_ma6'] = df[col].rolling(window=6).mean()
                ma_features.extend([f'{col}_ma3', f'{col}_ma6'])
        
        # Create trend features
        trend_features = []
        for col in self.feature_columns:
            if col in df.columns:
                df[f'{col}_trend'] = df[col].pct_change()
                trend_features.append(f'{col}_trend')
        
        # Combine all features
        feature_cols = (['Month_num', 'Year', 'Month_of_year'] + 
                       lag_features + ma_features + trend_features)
        
        # Remove rows with NaN values (due to lag and moving average features)
        df_clean = df.dropna()
        
        X = df_clean[feature_cols].values
        
        # Create target dictionary
        y_dict = {}
        for col in self.feature_columns + ['Total']:
            if col in df_clean.columns:
                y_dict[col] = df_clean[col].values
        
        return X, y_dict
    
    def _train_category_models(self, X_train, X_test, y_train, y_test, category):
        """Train and evaluate models for a specific category"""
        best_model = None
        best_score = float('inf')
        performance_results = {}
        
        # Scale features
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        self.scalers[category] = scaler
        
        for model_name, model in self.models.items():
            # Train model
            model.fit(X_train_scaled, y_train)
            
            # Make predictions
            y_pred = model.predict(X_test_scaled)
            
            # Calculate metrics
            mae = mean_absolute_error(y_test, y_pred)
            mse = mean_squared_error(y_test, y_pred)
            rmse = np.sqrt(mse)
            r2 = r2_score(y_test, y_pred)
            
            performance_results[model_name] = {
                'MAE': round(mae, 2),
                'RMSE': round(rmse, 2),
                'R²': round(r2, 4)
            }
            
            # Select best model based on RMSE
            if rmse < best_score:
                best_score = rmse
                best_model = model
        
        return best_model, best_score, performance_results
    
    def _generate_category_forecast(self, model, df, category, forecast_months):
        """Generate forecast for a specific category"""
        # Prepare the last known data point for forecasting
        df_extended = df.copy()
        df_extended['Month_dt'] = pd.to_datetime(df_extended['Month'])
        
        forecasts = []
        
        for i in range(forecast_months):
            # Get the last few rows for feature creation
            last_data = df_extended.tail(10).copy()
            
            # Create next month date
            last_date = last_data['Month_dt'].iloc[-1]
            next_date = last_date + pd.DateOffset(months=1)
            
            # Prepare features for next month
            next_features = self._prepare_next_month_features(last_data, category, next_date)
            
            # Scale features
            if category in self.scalers:
                next_features_scaled = self.scalers[category].transform([next_features])
            else:
                next_features_scaled = [next_features]
            
            # Make prediction
            prediction = model.predict(next_features_scaled)[0]
            
            # Ensure prediction is positive (expenses can't be negative)
            prediction = max(0, prediction)
            
            forecasts.append(prediction)
            
            # Add prediction to dataframe for next iteration
            new_row = {
                'Month': next_date.strftime('%Y-%m-%d'),
                'Month_dt': next_date,
                category: prediction
            }
            
            # Fill other categories with their average growth rate
            for col in self.feature_columns:
                if col != category and col in df_extended.columns:
                    # Calculate average growth rate
                    recent_values = df_extended[col].tail(6)
                    growth_rate = recent_values.pct_change().mean()
                    last_value = df_extended[col].iloc[-1]
                    new_row[col] = last_value * (1 + growth_rate)
            
            df_extended = pd.concat([df_extended, pd.DataFrame([new_row])], ignore_index=True)
        
        return forecasts
    
    def _prepare_next_month_features(self, last_data, category, next_date):
        """Prepare features for the next month prediction"""
        # Time-based features
        month_num = (next_date - last_data['Month_dt'].min()).days / 30.44
        year = next_date.year
        month_of_year = next_date.month
        
        features = [month_num, year, month_of_year]
        
        # Lag features
        for col in self.feature_columns:
            if col in last_data.columns:
                # lag1, lag2, lag3
                features.extend([
                    last_data[col].iloc[-1],  # lag1
                    last_data[col].iloc[-2] if len(last_data) > 1 else last_data[col].iloc[-1],  # lag2
                    last_data[col].iloc[-3] if len(last_data) > 2 else last_data[col].iloc[-1]   # lag3
                ])
        
        # Moving averages
        for col in self.feature_columns:
            if col in last_data.columns:
                # ma3, ma6
                ma3 = last_data[col].tail(3).mean()
                ma6 = last_data[col].tail(6).mean() if len(last_data) >= 6 else ma3
                features.extend([ma3, ma6])
        
        # Trend features
        for col in self.feature_columns:
            if col in last_data.columns:
                trend = last_data[col].pct_change().iloc[-1]
                if pd.isna(trend):
                    trend = 0
                features.append(trend)
        
        return features
    
    def _create_forecast_dataframe(self, df, forecasts, forecast_months):
        """Create forecast dataframe with all categories"""
        # Get the last date from historical data
        last_date = pd.to_datetime(df['Month'].iloc[-1])
        
        # Generate future dates
        future_dates = []
        for i in range(1, forecast_months + 1):
            future_date = last_date + pd.DateOffset(months=i)
            future_dates.append(future_date.strftime('%Y-%m-%d'))
        
        # Create forecast dataframe
        forecast_data = {'Month': future_dates}
        
        # Add forecasts for each category
        for category in self.feature_columns:
            if category in forecasts:
                forecast_data[category] = forecasts[category]
            else:
                # If no forecast available, use trend-based prediction
                recent_values = df[category].tail(6)
                growth_rate = recent_values.pct_change().mean()
                if pd.isna(growth_rate):
                    growth_rate = 0
                
                last_value = df[category].iloc[-1]
                category_forecast = []
                for i in range(forecast_months):
                    predicted_value = last_value * ((1 + growth_rate) ** (i + 1))
                    category_forecast.append(max(0, predicted_value))
                
                forecast_data[category] = category_forecast
        
        # Calculate total
        forecast_data['Total'] = [
            sum(forecast_data[cat][i] for cat in self.feature_columns if cat in forecast_data)
            for i in range(forecast_months)
        ]
        
        return pd.DataFrame(forecast_data)
