#!/usr/bin/env python3
"""
Simple test to validate the CSV data structure and basic functionality
"""

import csv
import os
from datetime import datetime

def test_csv_structure():
    """Test the structure of the provided CSV file"""
    print("🔍 Testing CSV Structure...")
    
    csv_file = 'company_expenses_non_linear.csv'
    
    if not os.path.exists(csv_file):
        print(f"❌ CSV file '{csv_file}' not found!")
        return False
    
    required_columns = ['Month', 'Miscellaneous', 'Financial', 'CapEx', 'COGS', 'Operating', 'Total']
    
    try:
        with open(csv_file, 'r') as file:
            reader = csv.DictReader(file)
            
            # Check headers
            headers = reader.fieldnames
            print(f"📋 Found columns: {headers}")
            
            missing_columns = set(required_columns) - set(headers)
            if missing_columns:
                print(f"❌ Missing required columns: {missing_columns}")
                return False
            
            print("✅ All required columns present")
            
            # Check data
            rows = list(reader)
            print(f"📊 Total rows: {len(rows)}")
            
            if len(rows) < 12:
                print("❌ Insufficient data (less than 12 months)")
                return False
            
            print("✅ Sufficient data for forecasting")
            
            # Check data types and values
            valid_rows = 0
            for i, row in enumerate(rows[:5]):  # Check first 5 rows
                try:
                    # Test date parsing
                    date_str = row['Month']
                    datetime.strptime(date_str, '%Y-%m-%d')
                    
                    # Test numeric values
                    for col in ['Miscellaneous', 'Financial', 'CapEx', 'COGS', 'Operating', 'Total']:
                        float(row[col])
                    
                    valid_rows += 1
                    
                except ValueError as e:
                    print(f"⚠️  Row {i+1} has invalid data: {e}")
            
            print(f"✅ {valid_rows}/5 sample rows are valid")
            
            # Show sample data
            print("\n📋 Sample data (first 3 rows):")
            for i, row in enumerate(rows[:3]):
                print(f"  Row {i+1}: {row['Month']} - Total: ${float(row['Total']):,.2f}")
            
            return True
            
    except Exception as e:
        print(f"❌ Error reading CSV: {e}")
        return False

def test_basic_calculations():
    """Test basic calculation logic"""
    print("\n🧮 Testing Basic Calculations...")
    
    # Test data consistency
    csv_file = 'company_expenses_non_linear.csv'
    
    try:
        with open(csv_file, 'r') as file:
            reader = csv.DictReader(file)
            rows = list(reader)
            
            inconsistent_rows = 0
            for i, row in enumerate(rows):
                try:
                    misc = float(row['Miscellaneous'])
                    financial = float(row['Financial'])
                    capex = float(row['CapEx'])
                    cogs = float(row['COGS'])
                    operating = float(row['Operating'])
                    total = float(row['Total'])
                    
                    calculated_total = misc + financial + capex + cogs + operating
                    difference = abs(total - calculated_total)
                    
                    if difference > 1.0:  # Allow small rounding differences
                        inconsistent_rows += 1
                        if inconsistent_rows <= 3:  # Show first 3 inconsistencies
                            print(f"⚠️  Row {i+1}: Total mismatch - Recorded: ${total:.2f}, Calculated: ${calculated_total:.2f}")
                
                except ValueError:
                    print(f"❌ Row {i+1}: Invalid numeric data")
                    return False
            
            if inconsistent_rows == 0:
                print("✅ All totals are consistent with category sums")
            else:
                print(f"⚠️  {inconsistent_rows} rows have total inconsistencies (will be auto-corrected)")
            
            return True
            
    except Exception as e:
        print(f"❌ Error in calculations: {e}")
        return False

def test_date_continuity():
    """Test date continuity and format"""
    print("\n📅 Testing Date Continuity...")
    
    csv_file = 'company_expenses_non_linear.csv'
    
    try:
        with open(csv_file, 'r') as file:
            reader = csv.DictReader(file)
            rows = list(reader)
            
            dates = []
            for row in rows:
                try:
                    date = datetime.strptime(row['Month'], '%Y-%m-%d')
                    dates.append(date)
                except ValueError:
                    print(f"❌ Invalid date format: {row['Month']}")
                    return False
            
            # Check if dates are sorted
            sorted_dates = sorted(dates)
            if dates == sorted_dates:
                print("✅ Dates are in chronological order")
            else:
                print("⚠️  Dates are not in chronological order (will be auto-sorted)")
            
            # Check date range
            start_date = min(dates)
            end_date = max(dates)
            print(f"📅 Date range: {start_date.strftime('%Y-%m')} to {end_date.strftime('%Y-%m')}")
            
            # Calculate months
            months = len(dates)
            years = months / 12
            print(f"📊 Data span: {months} months ({years:.1f} years)")
            
            if years >= 2:
                print("✅ Sufficient historical data for accurate forecasting")
            else:
                print("⚠️  Limited historical data - forecasts may be less accurate")
            
            return True
            
    except Exception as e:
        print(f"❌ Error in date analysis: {e}")
        return False

def test_flask_app_structure():
    """Test Flask application structure"""
    print("\n🌐 Testing Flask Application Structure...")
    
    required_files = [
        'app.py',
        'data_processor.py',
        'model_trainer.py',
        'requirements.txt',
        'templates/base.html',
        'templates/index.html',
        'templates/forecast.html',
        'templates/results.html'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ Missing files: {missing_files}")
        return False
    
    print("✅ All required application files present")
    
    # Check if uploads directory exists
    if not os.path.exists('uploads'):
        print("⚠️  uploads directory missing (will be created automatically)")
    else:
        print("✅ uploads directory exists")
    
    return True

def main():
    """Run all tests"""
    print("🚀 AI Expense Forecasting System - Basic Validation")
    print("=" * 60)
    
    tests = [
        ("CSV Structure", test_csv_structure),
        ("Basic Calculations", test_basic_calculations),
        ("Date Continuity", test_date_continuity),
        ("Flask App Structure", test_flask_app_structure)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All basic tests passed! The system structure is valid.")
        print("\n📋 Next Steps:")
        print("1. Install dependencies: pip install -r requirements.txt")
        print("2. Run the Flask app: python app.py")
        print("3. Open browser to: http://localhost:5000")
        print("4. Upload your CSV file and generate forecasts!")
    else:
        print("⚠️  Some tests failed. Please fix the issues above.")
    
    return passed == total

if __name__ == '__main__':
    main()
