{% extends "base.html" %}

{% block title %}Configure Forecast - AI Expense Forecasting{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <!-- Success Message -->
            <div class="alert alert-success">
                <h4 class="alert-heading">
                    <i class="fas fa-check-circle me-2"></i>
                    Data Successfully Processed!
                </h4>
                <p class="mb-0">Your CSV file has been validated and processed. Configure your forecast parameters below.</p>
            </div>

            <!-- Data Summary -->
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Data Summary
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="metric-card text-center">
                                <h3>{{ validation_results.data_info.total_records }}</h3>
                                <p class="mb-0">Total Records</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="metric-card text-center">
                                <h3>{{ validation_results.data_info.date_range.split(' to ')[0][:10] }}</h3>
                                <p class="mb-0">Start Date</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="metric-card text-center">
                                <h3>{{ validation_results.data_info.date_range.split(' to ')[1][:10] }}</h3>
                                <p class="mb-0">End Date</p>
                            </div>
                        </div>
                    </div>

                    {% if validation_results.warnings %}
                    <div class="alert alert-warning mt-3">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>Warnings:</h6>
                        <ul class="mb-0">
                            {% for warning in validation_results.warnings %}
                            <li>{{ warning }}</li>
                            {% endfor %}
                        </ul>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Data Preview -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>
                        Data Preview (First 5 Rows)
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        {{ data_preview|safe }}
                    </div>
                </div>
            </div>

            <!-- Forecast Configuration -->
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-cog me-2"></i>
                        Forecast Configuration
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('generate_forecast') }}" method="post" id="forecastForm">
                        <input type="hidden" name="processed_filename" value="{{ processed_filename }}">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-4">
                                    <label for="forecast_months" class="form-label">
                                        <i class="fas fa-calendar-alt me-2"></i>
                                        Number of Months to Forecast
                                    </label>
                                    <input type="number" class="form-control form-control-lg" 
                                           id="forecast_months" name="forecast_months" 
                                           min="1" max="24" value="6" required>
                                    <div class="form-text">
                                        Choose between 1 and 24 months. Recommended: 6-12 months for best accuracy.
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-4">
                                    <label class="form-label">
                                        <i class="fas fa-robot me-2"></i>
                                        AI Model Selection
                                    </label>
                                    <div class="alert alert-info">
                                        <i class="fas fa-magic me-2"></i>
                                        Our system automatically selects the best performing model 
                                        (Linear Regression or Random Forest) based on your data.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Forecast Range Preview -->
                        <div class="card bg-light mb-4">
                            <div class="card-body">
                                <h6><i class="fas fa-eye me-2"></i>Forecast Preview</h6>
                                <p class="mb-2">
                                    <strong>Historical Data:</strong> 
                                    {{ validation_results.data_info.date_range }}
                                </p>
                                <p class="mb-0">
                                    <strong>Forecast Period:</strong> 
                                    <span id="forecastRange">Next 6 months</span>
                                </p>
                            </div>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-success btn-lg" id="generateBtn">
                                <i class="fas fa-chart-line me-2"></i>
                                Generate AI Forecast
                                <div class="spinner-border spinner-border-sm ms-2 d-none" id="loadingSpinner"></div>
                            </button>
                            <a href="{{ url_for('index') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>
                                Upload Different File
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Model Information -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-brain me-2"></i>
                        AI Model Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-chart-line me-2"></i>Linear Regression</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-1"></i> Best for linear trends</li>
                                <li><i class="fas fa-check text-success me-1"></i> Fast and interpretable</li>
                                <li><i class="fas fa-check text-success me-1"></i> Good for stable patterns</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-tree me-2"></i>Random Forest</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-1"></i> Handles complex patterns</li>
                                <li><i class="fas fa-check text-success me-1"></i> Robust to outliers</li>
                                <li><i class="fas fa-check text-success me-1"></i> Captures seasonality</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const forecastMonths = document.getElementById('forecast_months');
    const forecastRange = document.getElementById('forecastRange');
    const generateBtn = document.getElementById('generateBtn');
    const loadingSpinner = document.getElementById('loadingSpinner');
    const forecastForm = document.getElementById('forecastForm');

    // Update forecast range preview
    forecastMonths.addEventListener('input', function() {
        const months = parseInt(this.value);
        if (months === 1) {
            forecastRange.textContent = 'Next 1 month';
        } else {
            forecastRange.textContent = `Next ${months} months`;
        }
    });

    // Handle form submission
    forecastForm.addEventListener('submit', function() {
        generateBtn.disabled = true;
        loadingSpinner.classList.remove('d-none');
        generateBtn.innerHTML = '<i class="fas fa-cog fa-spin me-2"></i>Generating Forecast...';
    });

    // Add input validation
    forecastMonths.addEventListener('input', function() {
        const value = parseInt(this.value);
        if (value < 1 || value > 24) {
            this.setCustomValidity('Please enter a value between 1 and 24');
        } else {
            this.setCustomValidity('');
        }
    });
});
</script>
{% endblock %}
