#!/usr/bin/env python3
"""
Demo script to show the AI Expense Forecasting System functionality
This script demonstrates the core logic without requiring heavy ML dependencies
"""

import csv
import json
from datetime import datetime, timedelta
import statistics

class SimpleForecaster:
    """Simple forecasting using trend analysis and moving averages"""
    
    def __init__(self):
        self.categories = ['Miscellaneous', 'Financial', 'CapEx', 'COGS', 'Operating']
    
    def load_csv_data(self, filename):
        """Load and parse CSV data"""
        data = []
        with open(filename, 'r') as file:
            reader = csv.DictReader(file)
            for row in reader:
                # Parse date
                date = datetime.strptime(row['Month'], '%Y-%m-%d')
                
                # Parse numeric values
                parsed_row = {'Month': date}
                for col in self.categories + ['Total']:
                    parsed_row[col] = float(row[col])
                
                data.append(parsed_row)
        
        return sorted(data, key=lambda x: x['Month'])
    
    def calculate_trends(self, data):
        """Calculate growth trends for each category"""
        trends = {}
        
        for category in self.categories + ['Total']:
            values = [row[category] for row in data]
            
            # Calculate month-over-month growth rates
            growth_rates = []
            for i in range(1, len(values)):
                if values[i-1] != 0:
                    growth_rate = (values[i] - values[i-1]) / values[i-1]
                    growth_rates.append(growth_rate)
            
            # Use median growth rate to avoid outlier influence
            if growth_rates:
                trends[category] = {
                    'avg_growth': statistics.median(growth_rates),
                    'recent_avg': statistics.mean(values[-6:]),  # Last 6 months average
                    'volatility': statistics.stdev(growth_rates) if len(growth_rates) > 1 else 0
                }
            else:
                trends[category] = {
                    'avg_growth': 0,
                    'recent_avg': statistics.mean(values),
                    'volatility': 0
                }
        
        return trends
    
    def generate_forecast(self, data, months):
        """Generate forecast for specified number of months"""
        trends = self.calculate_trends(data)
        
        # Get last date
        last_date = data[-1]['Month']
        
        forecast = []
        for i in range(1, months + 1):
            # Calculate next month date
            next_date = last_date + timedelta(days=32)
            next_date = next_date.replace(day=1)  # First day of month
            last_date = next_date
            
            # Forecast each category
            forecast_row = {'Month': next_date.strftime('%Y-%m-%d')}
            
            for category in self.categories:
                trend = trends[category]
                
                # Use recent average with trend adjustment
                base_value = trend['recent_avg']
                growth_factor = 1 + trend['avg_growth']
                
                # Apply some dampening for longer forecasts
                dampening = 0.95 ** i  # Reduce growth impact over time
                adjusted_growth = 1 + (trend['avg_growth'] * dampening)
                
                forecasted_value = base_value * adjusted_growth
                
                # Ensure positive values
                forecast_row[category] = max(0, forecasted_value)
            
            # Calculate total
            forecast_row['Total'] = sum(forecast_row[cat] for cat in self.categories)
            forecast.append(forecast_row)
        
        return forecast
    
    def analyze_data(self, data):
        """Analyze historical data and provide insights"""
        analysis = {
            'total_months': len(data),
            'date_range': f"{data[0]['Month'].strftime('%Y-%m')} to {data[-1]['Month'].strftime('%Y-%m')}",
            'category_stats': {}
        }
        
        for category in self.categories + ['Total']:
            values = [row[category] for row in data]
            analysis['category_stats'][category] = {
                'min': min(values),
                'max': max(values),
                'avg': statistics.mean(values),
                'recent_avg': statistics.mean(values[-6:]),
                'trend': 'increasing' if values[-1] > values[0] else 'decreasing'
            }
        
        return analysis

def demo_forecasting():
    """Demonstrate the forecasting system"""
    print("🚀 AI Expense Forecasting System - Demo")
    print("=" * 50)
    
    # Initialize forecaster
    forecaster = SimpleForecaster()
    
    # Load data
    print("📁 Loading historical data...")
    try:
        data = forecaster.load_csv_data('company_expenses_non_linear.csv')
        print(f"✅ Loaded {len(data)} months of data")
    except Exception as e:
        print(f"❌ Error loading data: {e}")
        return
    
    # Analyze data
    print("\n📊 Analyzing historical data...")
    analysis = forecaster.analyze_data(data)
    
    print(f"📅 Data period: {analysis['date_range']}")
    print(f"📈 Total months: {analysis['total_months']}")
    
    print("\n💰 Category Analysis:")
    for category, stats in analysis['category_stats'].items():
        if category != 'Total':
            print(f"  {category:12}: Avg ${stats['avg']:8,.0f} | Recent ${stats['recent_avg']:8,.0f} | {stats['trend']}")
    
    total_stats = analysis['category_stats']['Total']
    print(f"  {'Total':12}: Avg ${total_stats['avg']:8,.0f} | Recent ${total_stats['recent_avg']:8,.0f} | {total_stats['trend']}")
    
    # Generate forecast
    forecast_months = 6
    print(f"\n🔮 Generating {forecast_months}-month forecast...")
    
    try:
        forecast = forecaster.generate_forecast(data, forecast_months)
        print("✅ Forecast generated successfully!")
        
        print(f"\n📈 {forecast_months}-Month Expense Forecast:")
        print("-" * 80)
        print(f"{'Month':12} {'Misc':>10} {'Financial':>10} {'CapEx':>10} {'COGS':>10} {'Operating':>10} {'Total':>12}")
        print("-" * 80)
        
        for row in forecast:
            print(f"{row['Month'][:7]:12} "
                  f"{row['Miscellaneous']:10,.0f} "
                  f"{row['Financial']:10,.0f} "
                  f"{row['CapEx']:10,.0f} "
                  f"{row['COGS']:10,.0f} "
                  f"{row['Operating']:10,.0f} "
                  f"{row['Total']:12,.0f}")
        
        # Calculate forecast summary
        total_forecast = sum(row['Total'] for row in forecast)
        avg_monthly = total_forecast / forecast_months
        
        print("-" * 80)
        print(f"📊 Forecast Summary:")
        print(f"   Total {forecast_months}-month expenses: ${total_forecast:,.0f}")
        print(f"   Average monthly expenses: ${avg_monthly:,.0f}")
        
        # Compare with recent historical average
        recent_avg = analysis['category_stats']['Total']['recent_avg']
        change_pct = ((avg_monthly - recent_avg) / recent_avg) * 100
        
        if change_pct > 0:
            print(f"   Expected change: +{change_pct:.1f}% increase from recent average")
        else:
            print(f"   Expected change: {change_pct:.1f}% decrease from recent average")
        
    except Exception as e:
        print(f"❌ Error generating forecast: {e}")
        return
    
    print("\n🎯 Key Insights:")
    print("• Forecast uses trend analysis and moving averages")
    print("• Growth rates are dampened for longer-term predictions")
    print("• Recent data is weighted more heavily than older data")
    print("• All forecasted values are constrained to be positive")
    
    print("\n📋 Next Steps:")
    print("1. Install full dependencies for advanced ML models:")
    print("   pip install -r requirements.txt")
    print("2. Run the web application:")
    print("   python app.py")
    print("3. Access the web interface at: http://localhost:5000")
    print("4. Upload CSV files and get interactive forecasts!")

if __name__ == '__main__':
    demo_forecasting()
